package org.biosino.lf.mash.mashweb.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.BiogeographyCreateDTO;
import org.biosino.lf.mash.mashweb.dto.BiogeographyResultQueryDTO;
import org.biosino.lf.mash.mashweb.service.CacheService;
import org.biosino.lf.mash.mashweb.service.GenomicService;
import org.biosino.lf.mash.mashweb.util.kegg.KeggEntry;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.biosino.lf.mash.mashweb.vo.SelectItemVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/genomic")
@RequiredArgsConstructor
public class GenomicController extends BaseController {
    private final GenomicService genomicService;


    @RequestMapping("/biogeography")
    public AjaxResult createBiogeography(@RequestBody BiogeographyCreateDTO paramsDTO) {
        String id = genomicService.createBiogeography(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/functionAnalysis")
    public AjaxResult createFunctionAnalysis() {
        return AjaxResult.success();
    }

    @RequestMapping("/getTableResult")
    public AjaxResult getTableResult(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Page<SamplesBioGeographyVO> result = genomicService.getTableResult(queryDTO);
        return AjaxResult.success(result);
    }

    @RequestMapping("/getPathKoToNameList")
    public AjaxResult getPathKoToNameList() {
        Map<String, String> pathKoPathNameMap = CacheService.pathKoPathNameMap;
        List<SelectItemVO> list = new ArrayList<>();
        pathKoPathNameMap.forEach((k, v) -> {
            SelectItemVO itemVO = new SelectItemVO();
            itemVO.setValue(k);
            itemVO.setLabel(v);
            list.add(itemVO);
        });
        return AjaxResult.success(list);
    }

    @RequestMapping("/getPathwayName")
    public AjaxResult getPathway() {
        Map<String, List<KeggEntry>> pathToKeggEntryMap = CacheService.pathToKeggEntryMap;
        List<SelectItemVO> list = new ArrayList<>();
        pathToKeggEntryMap.forEach((x, y) -> {
            KeggEntry keggEntry = y.get(0);
            String description = keggEntry.getDescription();
            SelectItemVO itemVO = new SelectItemVO();
            itemVO.setValue(x);
            itemVO.setLabel(description);
            list.add(itemVO);
        });
        return AjaxResult.success(list);
    }

}
