package org.biosino.lf.mash.mashweb.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.BiogeographyCreateDTO;
import org.biosino.lf.mash.mashweb.dto.BiogeographyResultQueryDTO;
import org.biosino.lf.mash.mashweb.service.DiversityService;
import org.biosino.lf.mash.mashweb.vo.SamplesBioGeographyVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/diversity")
@RequiredArgsConstructor
public class DiversityController extends BaseController {

    private final DiversityService diversityService;

    @RequestMapping("/biogeography")
    public AjaxResult createBiogeography(@RequestBody BiogeographyCreateDTO paramsDTO) {
        String id = diversityService.createBiogeography(paramsDTO);
        return AjaxResult.success(id);
    }

    @RequestMapping("/speciesDiversity")
    public AjaxResult createDiversity() {
        return AjaxResult.success();
    }

    @RequestMapping("/getTableResult")
    public AjaxResult getTableResult(@RequestBody BiogeographyResultQueryDTO queryDTO) {
        Page<SamplesBioGeographyVO> result = diversityService.getTableResult(queryDTO);
        return AjaxResult.success(result);
    }
}
